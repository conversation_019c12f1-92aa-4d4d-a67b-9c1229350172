"use client"
import { BorderBeam } from "./magicui/border-beam";
import { motion } from "framer-motion"
import Image from "next/image";
import { Tabs } from "../components/ui/tabs";
import Link from "next/link";
import BlurIn from "./magicui/blur-in"
import GradualSpacing from "@/components/magicui/gradual-spacing";

export default function IconCloudDemo() {
    return (
        <div className="section h-full">
            <div className="relative h-[100svh] w-full flex items-center justify-center overflow-hidden bg-purple-500">
                <div className="absolute top-0 h-full w-full z-10 hidden lg:flex items-center justify-center  ">
                    <motion.img
                        initial={{ translateX: 200 }}
                        whileInView={{ translateX: 0 }}
                        transition={{ duration: 1, type: "spring", stiffness: 20 }}
                        src="5.png" className="z-50 absolute h-40 right-0" />
                    <motion.img
                        initial={{ translateX: -200 }}
                        whileInView={{ translateX: -40 }}
                        transition={{ duration: 1, type: "spring", stiffness: 20 }}
                        src="4.png" className="z-50 left-0 absolute h-40 -translate-x-6" />
                </div>
                <motion.div
                    whileInView={{ translateY: 0 }}
                    initial={{ translateY: -130 }}
                    transition={{ duration: 1, type: "spring", stiffness: 20 }}
                    className="lg:hidden flex absolute  top-0" >
                    <img
                        src="5.png" className="z-50  h-40 -rotate-45 translate-x-48" />
                </motion.div>
                <motion.div
                    whileInView={{ translateY: 20 }}
                    initial={{ translateY: 150 }}
                    transition={{ duration: 1, type: "spring", stiffness: 20 }}
                    className="lg:hidden flex absolute  bottom-0">
                    <img
                        src="4.png" className="z-50  h-40 -rotate-45 -translate-x-48" />
                </motion.div>
                <div className="flex w-max items-end flex-col justify-end absolute z-50 top-0 left-0 m-10">
                    <GradualSpacing
                        className="font-display text-center text-4xl font-bold tracking-[-0.1em]  tex-white md:text-7xl md:leading-[5rem]  text-black"
                        text="BOLDBAT's Side Projects"
                    />
                </div>
                <div className="w-11/12 lg:w-3/5 flex  lg:items-start items-center lg:justify-center  lg:-translate-y-12 h-full  z-50  ">
                    <TabsDemo />
                </div>
            </div>
        </div>
    );
}



export function TabsDemo() {

    return (
        <div className="h-[20rem] md:h-[40rem] [perspective:1000px] relative b flex flex-col max-w-5xl mx-auto w-full  items-start justify-start lg:my-40  lg:translate-y-8  ">
            <Tabs tabs={stack.map((v) => {
                return {
                    title: v.name,
                    value: v.name,
                    content: <div className="w-full overflow-hidden relative rounded-xl  text-white text-xl md:text-4xl font-bold h-52 lg:h-full  lg:p-10 lg:bg-purple-400 lg:shadow-purple-400 lg:shadow-[0px_0px_25px_5px] ">
                        <p className="lg:block hidden">{v.name} </p>
                        <DummyContent path={v.url} url={v.images[0]} />
                    </div>
                }
            })} />
        </div>
    );
}

const DummyContent = ({ url, path }) => {
    return (
        <Link href={`${path}`}>
            <img
                src={url}
                alt="dummy image"
                className="object-cover object-left-top  absolute inset-x-0  rounded-xl mx-auto  lg:w-[90%] lg:mt-5"
            />
        </Link>
    );
};


const stack = [{
    "name": "AI Car Sharing App",
    "description": "🚗 Intelligent vehicle recognition and counting system using YOLOv8. Optimizes shared mobility solutions with advanced computer vision and AI-driven efficiency tracking.",
    "tags": "python yolov8 computer-vision ai tensorflow pytorch",
    "url": "#",
    "images": [
        "https://images.unsplash.com/photo-1549924231-f129b911e442?w=800&h=600&fit=crop",
        "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop"
    ],
    "md": 1
},
{
    "name": "AI Career Planning App",
    "description": "🎯 Personalized career guidance platform powered by AI. Provides intelligent career advice and planning based on user skills, interests, and market trends.",
    "tags": "nodejs react ai machine-learning career-planning",
    "url": "#",
    "images": [
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop",
        "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=600&fit=crop"
    ],
    "md": 2
},
{
    "name": "AI Chatbot Game",
    "description": "🎮 Educational 2D game featuring an integrated AI chatbot NPC. Designed to teach anti-corruption strategies through interactive gameplay and intelligent conversations.",
    "tags": "game-development ai-chatbot education flutter dart",
    "url": "#",
    "images": [
        "https://images.unsplash.com/photo-1511512578047-dfb367046420?w=800&h=600&fit=crop",
        "https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=800&h=600&fit=crop"
    ],
    "md": 3
},
{
    "name": "Mongolia Guide App",
    "description": "🇲🇳 Comprehensive guide for freelancers and digital nomads in Mongolia. Features coworking spaces, local laws, freelance opportunities, and cultural insights.",
    "tags": "flutter mobile-app travel guide mongodb nodejs",
    "url": "#",
    "images": [
        "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&h=600&fit=crop",
        "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop"
    ],
    "md": 4
}
]