@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  overflow: hidden;
  font-family: Inter;
  scroll-behavior: smooth;
}

/* .scroller {
  overflow-y: scroll;
  scroll-snap-type: y mandatory;
}

.scroller section {
  scroll-snap-align: start;
} */

.fp-watermark {
  display: none !important;
}

@layer base {
  :root {
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
  }
}

::-webkit-scrollbar {
  margin: 10px;
  @apply bg-transparent;
  width: 8px;
  height: 8px !important;
  /* adjust as needed */
}

::-webkit-scrollbar-track {
  background-color: white;
  /* set to desired background color for the scrollbar holder */
}

::-webkit-scrollbar-thumb {
  background: hsl(0, 0%, 0%);
  /* set to desired gradient for the scrollbar thumb */
  border-radius: 6px;
  /* adjust as needed */
}

