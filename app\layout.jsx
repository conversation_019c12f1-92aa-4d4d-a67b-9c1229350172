import "./globals.css";
import { Analytics } from "@vercel/analytics/react"

export const metadata = {
  title: "BOLDBAT.Khuukhenduu - Full-Stack Developer",
  description: "Discover the exceptional full-stack development skills of BOLDBAT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CEO of Oyu Intelligence LLC. Specializing in AI/ML, Vue, React, Node.js, Go, MongoDB, and React Native. Expert in web development and AI projects. Contact BOLDBAT.Khuukhenduu for your next project.",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter" />
        <link rel="icon" href="/1.jpg" sizes="any" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body className="h-full w-full ">
        <Analytics />
        {children}
      </body>
    </html>
  );
}
