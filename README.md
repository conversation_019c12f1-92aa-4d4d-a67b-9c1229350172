# Colorful Port

A vibrant and interactive portfolio website showcasing full-stack development skills and AI/ML expertise.

## About

This portfolio website represents the work and expertise of **BOLDBA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>**, CEO of **Oyu Intelligence LLC**, based in Mongolia. The site demonstrates proficiency in modern web technologies, AI/ML development, and creative design.

## Features

- **Interactive Full-Page Scrolling**: Smooth navigation between sections
- **Responsive Design**: Optimized for all device sizes
- **Modern Animations**: Powered by Framer Motion and custom CSS
- **Tech Stack Showcase**: Visual representation of technical skills
- **Project Portfolio**: Highlighting AI and web development projects
- **Contact Integration**: Direct links to professional profiles and contact methods

## Tech Stack

- **Frontend**: Next.js 14, React 18, Tailwind CSS
- **Animations**: Framer Motion, GSAP
- **UI Components**: Custom components with MagicUI
- **Deployment**: Vercel-ready configuration

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, pnpm, or bun

### Installation

1. Clone the repository:
```bash
git clone https://github.com/boldbat/colorful-port.git
cd colorful-port
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
colorful-port/
├── app/                 # Next.js app directory
├── components/          # React components
├── lib/                 # Utility functions and data
├── public/              # Static assets
└── tailwind.config.js   # Tailwind configuration
```

## Contact

**BOLDBAT.Khuukhenduu**
CEO, Oyu Intelligence LLC
📧 <EMAIL>
🔗 [LinkedIn](https://linkedin.com/in/boldbat)
🐙 [GitHub](https://github.com/boldbat)

## License

© 2024 BOLDBAT.Khuukhenduu / Oyu Intelligence LLC. All rights reserved.
